<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ConfirmPart.aspx.cs" Inherits="WebApplication1.Flow.ConfirmPart" %>

<!DOCTYPE html>
<head runat="server">
<meta charset="utf-8">
    <title>共用料确认</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
    <style>
        /* ===== 页面基础样式重置 ===== */
        .layuimini-container {
            padding-bottom: 80px !important;
        }

        /* ===== 表格样式重置 ===== */
        .layui-table-view {
            margin: 0 !important;
            border: 1px solid #e6e6e6;
        }

        .layui-table {
            margin: 0 !important;
            width: 100% !important;
        }

        .layui-table-box {
            overflow: visible !important;
        }

        .layui-table-main {
            overflow: auto !important;
        }

        /* ===== 表格单元格统一样式 ===== */
        .layui-table-cell {
            height: 50px !important;
            line-height: 50px !important;
            padding: 0 8px !important;
            overflow: visible !important;
            vertical-align: middle !important;
        }

        .layui-table tr {
            height: 50px !important;
        }

        /* ===== 表格标题居中 ===== */
        .layui-table-header .layui-table-cell {
            text-align: center !important;
            font-weight: bold !important;
            background-color: #f8f9fa !important;
        }

        /* ===== Checkbox样式修复 ===== */
        .layui-table-cell[lay-event="layTableAllChoose"],
        .layui-table-cell[lay-event="layTableCheck"] {
            text-align: center !important;
            padding: 0 !important;
        }

        .layui-form-checkbox[lay-skin="primary"] {
            margin: 0 !important;
            position: relative !important;
            top: 0 !important;
            transform: none !important;
            vertical-align: middle !important;
        }

        /* ===== 物料组Select-Input样式 ===== */
        .material-group-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .material-group-container .select-input {
            padding: 0;
            margin: 0;
        }

        .material-group-container .select-input-content {
            position: relative;
            width: 100%;
            margin: 0;
        }

        .material-group-container .select-input-container {
            width: 100%;
            height: 36px;
            line-height: 36px;
        }

        .material-group-container .select-input-input {
            width: 100%;
            height: 36px;
            line-height: 36px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            padding: 0 25px 0 8px;
            font-size: 12px;
            box-sizing: border-box;
        }

        .material-group-container .select-input-input:focus {
            border-color: #009688;
        }

        .material-group-container .select-input-icon {
            right: 8px;
            top: 50%;
            margin-top: -3px;
        }

        .material-group-container .select-input-body {
            z-index: 999999;
        }

        /* ===== 匹配料号下拉框样式 ===== */
        .match-select-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .match-select-wrapper .layui-form-select {
            width: 100%;
        }

        .match-select-wrapper .layui-form-select .layui-input {
            height: 36px;
            line-height: 36px;
            font-size: 12px;
        }

        .match-select-wrapper .layui-form-select dl {
            min-width: 300px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 999999;
        }

        .match-select-wrapper .layui-form-select dl dd {
            padding: 8px 10px;
            font-size: 12px;
            border-bottom: 1px solid #f5f5f5;
            white-space: nowrap;
        }

        .match-select-wrapper .layui-form-select dl dd:hover {
            background-color: #f2f2f2;
        }

        /* ===== 匹配料号选项标识样式 ===== */
        .multiple-indicator,
        .single-indicator {
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10;
        }

        .multiple-indicator {
            background-color: #ff9800;
        }

        .single-indicator {
            background-color: #4caf50;
        }

        /* ===== BOM描述单元格样式 ===== */
        .bom-desc-cell {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            max-width: 200px;
        }

        /* ===== 固定底部按钮样式 ===== */
        .fixed-bottom-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            border-top: 1px solid #e6e6e6;
            padding: 15px 20px;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .action-buttons {
            text-align: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .action-buttons .layui-btn {
            padding: 12px 30px;
            font-size: 14px;
            border-radius: 4px;
            min-width: 120px;
            height: auto;
            line-height: 1.4;
        }

        .action-buttons .layui-btn .layui-icon {
            margin-right: 5px;
            vertical-align: middle;
        }

        /* ===== 快捷操作区域样式 ===== */
        .layui-form-item {
            margin-bottom: 15px;
        }

        .layui-form-label {
            width: 100px;
            text-align: right;
            padding: 9px 15px;
        }

        .layui-input-inline {
            width: 200px;
            margin-right: 10px;
        }

        .layui-btn {
            margin-left: 10px;
        }

        /* ===== 响应式调整 ===== */
        @media (max-width: 768px) {
            .layui-form-label {
                width: 80px;
                padding: 9px 10px;
            }

            .layui-input-inline {
                width: 150px;
            }

            .action-buttons .layui-btn {
                padding: 10px 20px;
                min-width: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="layuimini-container">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
                <legend>快捷操作</legend>
                <div style="margin: 10px 10px 10px 10px">
                    <form class="layui-form layui-form-pane" id="rfqForm">

                        <div class="layui-form-item">
                            <label class="layui-form-label">物料组名</label>
                            <div class="layui-input-inline">
                                <select id="MaterialType" lay-filter="materialTypeSelect">
                                    <option value="">请选择需要填充的物料组</option>
                                </select>
                            </div>
                            <button type="button" class="layui-btn layui-btn-normal" id="CopyGroup">一键填充物料组</button>
                            <button type="button" class="layui-btn layui-btn-primary" id="RefreshGroups">刷新物料组</button>
                        </div>
                    </form>
                </div>


            </fieldset>
        

        

        <table class="layui-hide" id="ComfirmTable" lay-filter="ComfirmTableFilter"></table>



    </div>

        <!-- 固定在底部的操作按钮区域 -->
        <div class="fixed-bottom-actions">
            <div class="action-buttons">
                <button type="button" id="nextbtn" class="layui-btn layui-btn-normal">
                    <i class="layui-icon layui-icon-next"></i> 下一步
                </button>
            </div>
        </div>
</div>
</body>
<script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
<script src="../js/navigate.js" charset="utf-8"></script>
<script src="../js/lay-module/select-input2.x-1.0.2/dist/selectInput.js" charset="utf-8"></script>
<script>
    layui.use(['form', 'table', 'upload', 'element'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        function getQueryParam(name) {
            // 保留原有的参数获取逻辑
            var hash = window.location.hash;
            var hashParams = hash.substring(hash.indexOf('?') + 1);
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = hashParams.match(reg);

            if (r != null) {
                return decodeURIComponent(r[2]);
            }

            var queryParams = window.location.search.substr(1);
            r = queryParams.match(reg);
            if (r != null) {
                return decodeURIComponent(r[2]);
            }

            return null;
        }
        // 从URL获取RFQNo
        var RFQNo = getQueryParam('RFQNo');
        console.log(RFQNo);

        // 用一个对象来存储每个选项的完整信息
        var optionsData = {};
        // 在页面添加必要的样式
        // 物料组数据存储
        var materialGroupsData = [];

        // 获取物料组数据
        function loadMaterialGroups() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: '../ashx/ConfirmControl.ashx?action=GetMaterialGroups',
                    type: 'GET',
                    success: function (response) {
                        var data = JSON.parse(response);
                        if (data.code === 0) {
                            materialGroupsData = data.data.map(item => item.GroupName);
                            resolve(materialGroupsData);
                        } else {
                            layer.msg('获取物料组数据失败: ' + data.msg);
                            reject(data.msg);
                        }
                    },
                    error: function () {
                        layer.msg('获取物料组数据失败');
                        reject('网络错误');
                    }
                });
            });
        }



        function getcolumndata() {  //type=1 加载数据和列 type=0 仅加载列
            var index = layer.load(1, { shade: [0.5, '#fff'] });  // 显示加载动画
            console.log('添加前');
            columns = [];
            $.ajax({
                url: '../ashx/ConfirmControl.ashx?action=QueryColumn&RFQNo=' + RFQNo,
                type: 'POST',
                success: function (response) {

                    console.log(response);
                    if (JSON.parse(response).result == 'success') {
                        var fielddata = JSON.parse(response).col; // 假设返回的是JSON格式的数据
                        var modeldata = JSON.parse(response).models;
                        console.log(modeldata);
                        columns.push({ type: 'checkbox', fixed: 'left'});
                        columns.push({ field: 'Id', minWidth: 50, title: 'Id', hide: true });
                        // 添加MATCH相关列
                        columns.push(
                            {
                                field: 'MATERIAL_GROUP',
                                title: '物料组',
                                width: 150,
                                templet: function (d) {
                                    var uniqueId = 'material-group-' + d.Id;
                                    var html = '<div class="material-group-container">';
                                    html += '<div id="' + uniqueId + '" data-id="' + d.Id + '"></div>';
                                    html += '</div>';
                                    return html;
                                }
                            },
                            {
                                field: 'MATCH_PARTNUMBER',
                                title: '匹配料号',
                                width: 250,
                                templet: function (d) {
                                    var currentValue = d.MATCH_PARTNUMBER || '';
                                    var selectHtml = '<div class="match-select-wrapper" data-id="' + d.Id + '">';
                                    selectHtml += '<select lay-filter="matchSelect" class="match-select layui-select" lay-search data-id="' + d.Id + '">';
                                    selectHtml += '<option value="' + currentValue + '">' + (currentValue || '请选择匹配料号') + '</option>';
                                    selectHtml += '</select>';
                                    selectHtml += '<span class="match-select-arrow"></span>';
                                    selectHtml += '</div>';
                                    return selectHtml;
                                }
                            },
                            { field: 'MATCH_CUST', title: '客户料号', width: 150 },
                            { field: 'MATCH_MANUFACTURER', title: '制造商', width: 150 },
                            { field: 'MATCH_MPN', title: 'MPN', width: 150 }

                        );
                        if (fielddata.length > 0) {

                            fielddata.forEach(function (fd) {
                                if (fd.DatabaseField == 'BOM_DESC') {
                                    columns.push({
                                        field: fd.DatabaseField, align: 'center', minWidth: 170, title: fd.DatabaseField, templet: function (d) {
                                            // 使用data属性存储完整文本
                                            return '<div class="bom-desc-cell" data-content="' +
                                                (d.BOM_DESC || '').replace(/"/g, '&quot;') +
                                                '" lay-tips="' + (d.BOM_DESC || '').replace(/"/g, '&quot;') +
                                                '">' + d.BOM_DESC + '</div>';
                                        }
                                    });
                                }
                                else {
                                    columns.push({ field: fd.DatabaseField, align: 'center', minWidth: 200, title: fd.DatabaseField });
                                }
                            });
                        }
                        if (modeldata.length > 0) {
                            for (var key in modeldata) {

                                columns.push({ field: modeldata[key], align: 'center', minWidth: 150, title: modeldata[key] });
                            }

                        }


                        $.ajax({
                            url: '../ashx/ConfirmControl.ashx?action=QueryBom&RFQNo=' + RFQNo,
                            type: 'POST',
                            success: function (bomdata) {
                                console.log(columns);
                                rendertable(JSON.parse(bomdata), columns);
                                layer.close(index);  // 关闭加载动画
                            },
                            error: function (data) {
                                layer.alert(data.msg);
                                layer.close(index);  // 关闭加载动画
                            }
                        });



                    }
                    else {
                        layer.alert("RFQNo为空,请先导入项目信息");
                        layer.close(index);  // 关闭加载动画
                    }

                },
                error: function (data) {
                    layer.alert(data.msg);
                    layer.close(index);  // 关闭加载动画
                    $('button').prop('disabled', false);  // 恢复所有按钮
                }
            });


        }
        // 初始化快捷操作区域的物料组选择
        function initializeQuickFillSelect() {
            loadMaterialGroups().then(function(groups) {
                var $select = $('#MaterialType');
                $select.empty().append('<option value="">请选择需要填充的物料组</option>');
                groups.forEach(function(group) {
                    $select.append('<option value="' + group + '">' + group + '</option>');
                });
                form.render('select');
            });
        }

        // 物料组select-input实例存储
        var materialGroupInstances = {};

        // 重写物料组输入框初始化函数
        function initializeMaterialGroupInputs() {
            // 为每个物料组创建select-input实例
            $('.material-group-container [id^="material-group-"]').each(function() {
                var $container = $(this);
                var elemId = $container.attr('id');
                var dataId = $container.data('id');

                // 如果实例已存在，先销毁
                if (materialGroupInstances[elemId]) {
                    try {
                        materialGroupInstances[elemId].emptyValue();
                    } catch(e) {}
                }

                // 获取当前值
                var currentValue = '';
                var tableData = table.cache['ComfirmTable'];
                if (tableData) {
                    var rowData = tableData.find(function(row) {
                        return row.Id === dataId;
                    });
                    if (rowData && rowData.MATERIAL_GROUP) {
                        currentValue = rowData.MATERIAL_GROUP;
                    }
                }

                // 创建select-input实例
                var instance = window.selectInput.render({
                    elem: '#' + elemId,
                    data: materialGroupsData.map(function(group) {
                        return {name: group, value: group};
                    }),
                    initValue: currentValue,
                    placeholder: '请输入或选择物料组',
                    hasSelectIcon: true,
                    localSearch: true,
                    ignoreCase: true,
                    clickClose: true,
                    height: '150px',
                    invisibleMode: true, // 允许输入不在列表中的值
                    onClick: function(item) {
                        // 选择项目时的回调
                        console.log('选择了物料组:', item);
                    },
                    onInput: function(value) {
                        // 输入时的回调
                        console.log('输入物料组:', value);
                    },
                    onBlur: function(value) {
                        // 失去焦点时验证
                        var result = instance.getValue();
                        if (result.value && !result.isSelect && !materialGroupsData.includes(result.value)) {
                            instance.emptyValue();
                            layer.msg('请输入有效的物料组值，或从下拉列表中选择', {icon: 2});
                        }
                    }
                });

                materialGroupInstances[elemId] = instance;
            });
        }


        function rendertable(data, columns) {
            // 确保清除可能存在的旧实例
            if (table.cache['ComfirmTable']) {
                delete table.cache['ComfirmTable'];
            }
            table.render({
                elem: '#ComfirmTable',
                cols: [columns],
                data: data,
                page: false,
                height: Math.min(window.innerHeight - 250, 600), // 设置合适的高度
                limit: 10000, // 设置一个很大的数值，显示所有数据
                done: function (res) {
                    

                    // 初始化其他功能
                    $('.material-group-dropdown').hide();

                    // 处理提示框
                    $('.bom-desc-cell').each(function () {
                        var content = $(this).data('content');
                        if (content) {
                            layui.use('layer', function () {
                                var layer = layui.layer;
                                $(this).on('mouseenter', function () {
                                    layer.tips(content, this, {
                                        tips: [1, '#3595CC'],
                                        time: 0,
                                        maxWidth: 500
                                    });
                                }).on('mouseleave', function () {
                                    layer.closeAll('tips');
                                });
                            });
                        }
                    });
                    setTimeout(function () {
                        loadMatchPartnumberOptions();
                        initializeMaterialGroupInputs();

                        // 默认全选所有物料 - 使用更可靠的方法
                        setTimeout(function() {
                            try {
                                // 方法1：直接查找并点击全选checkbox
                                var $headerCheckbox = $('.layui-table-header input[lay-filter="layTableAllChoose"]');
                                if ($headerCheckbox.length > 0) {
                                    $headerCheckbox[0].click();
                                    console.log('方法1成功：点击了全选checkbox');
                                } else {
                                    // 方法2：查找layui的checkbox组件
                                    var $layuiCheckbox = $('.layui-table-header .layui-form-checkbox[lay-skin="primary"]');
                                    if ($layuiCheckbox.length > 0) {
                                        $layuiCheckbox[0].click();
                                        console.log('方法2成功：点击了layui checkbox组件');
                                    } else {
                                        // 方法3：查找任何checkbox
                                        var $anyCheckbox = $('.layui-table-header input[type="checkbox"]');
                                        if ($anyCheckbox.length > 0) {
                                            $anyCheckbox[0].click();
                                            console.log('方法3成功：点击了任意checkbox');
                                        } else {
                                            console.log('未找到全选checkbox');
                                        }
                                    }
                                }
                            } catch(e) {
                                console.error('全选失败:', e);
                            }
                        }, 800);
                    }, 100);
                }
                   
                
            });
        }

        // 在表格初始化完成后添加以下代码
        table.on('done', function (res, curr, count) {
            // 同步固定列和主体的高度
            var fixedNodes = document.querySelectorAll('.layui-table-fixed .layui-table-body tr');
            var mainNodes = document.querySelectorAll('.layui-table-main tr');

            for (var i = 0; i < fixedNodes.length; i++) {
                if (mainNodes[i]) {
                    var mainHeight = mainNodes[i].offsetHeight;
                    fixedNodes[i].style.height = mainHeight + 'px';
                }
            }

            // 监听表格滚动
            $('.layui-table-main').on('scroll', function () {
                var top = $(this).scrollTop();
                $('.layui-table-fixed .layui-table-body').scrollTop(top);
            });
        });

        // 在选择框变化时重新同步高度
        form.on('select(matchSelect)', function (data) {
            setTimeout(function () {
                table.resize('ComfirmTable');
            }, 10);
        });

        // 缓存匹配料号数据，避免重复加载
        var matchDataCache = {};
        var isLoadingMatchData = false;

        function loadMatchPartnumberOptions() {
            if (isLoadingMatchData) {
                return; // 如果正在加载，直接返回
            }

            isLoadingMatchData = true;

            // 收集所有需要加载的ID
            var idsToLoad = [];
            $('.match-select').each(function () {
                var rowId = $(this).data('id');
                if (!matchDataCache[rowId]) {
                    idsToLoad.push(rowId);
                }
            });

            if (idsToLoad.length === 0) {
                // 如果都已缓存，直接渲染
                renderMatchSelects();
                isLoadingMatchData = false;
                return;
            }

            // 批量加载数据
            var loadPromises = idsToLoad.map(function(rowId) {
                return new Promise(function(resolve, reject) {
                    $.ajax({
                        url: '../ashx/ConfirmControl.ashx?action=GetMatchOptions',
                        type: 'POST',
                        data: { id: rowId },
                        success: function (response) {
                            try {
                                var data = JSON.parse(response);
                                matchDataCache[rowId] = data;
                                optionsData[rowId] = data;
                                resolve({rowId: rowId, data: data});
                            } catch(e) {
                                reject(e);
                            }
                        },
                        error: function () {
                            reject(new Error('加载失败'));
                        }
                    });
                });
            });

            // 等待所有数据加载完成
            Promise.all(loadPromises).then(function(results) {
                renderMatchSelects();
                isLoadingMatchData = false;
            }).catch(function(error) {
                console.error('批量加载匹配料号失败:', error);
                isLoadingMatchData = false;
            });
        }

        function renderMatchSelects() {
            $('.match-select').each(function () {
                var select = $(this);
                var rowId = select.data('id');
                var currentValue = select.find('option:first').text();
                var wrapper = select.closest('.match-select-wrapper');
                var data = matchDataCache[rowId] || [];

                // 如果有数据且当前值为空或默认值，则自动选择第一项
                if (data.length > 0 && (!currentValue || currentValue === '请选择匹配料号' || currentValue === '加载中...' || currentValue === '请选择')) {
                    var firstItem = data[0];
                    currentValue = firstItem.MATCH_PARTNUMBER;

                    var $tr = select.closest('tr');
                    var index = $tr.data('index');
                    var tableData = table.cache['ComfirmTable'];

                    if (tableData && tableData[index]) {
                        Object.assign(tableData[index], {
                            MATCH_PARTNUMBER: firstItem.MATCH_PARTNUMBER,
                            MATCH_CUST: firstItem.MATCH_CUST,
                            MATCH_MANUFACTURER: firstItem.MATCH_MANUFACTURER,
                            MATCH_MPN: firstItem.MATCH_MPN
                        });

                        $tr.find('td[data-field="MATCH_CUST"] div').html(firstItem.MATCH_CUST === 'NA' ? '' : firstItem.MATCH_CUST);
                        $tr.find('td[data-field="MATCH_MANUFACTURER"] div').html(firstItem.MATCH_MANUFACTURER === 'NA' ? '' : firstItem.MATCH_MANUFACTURER);
                        $tr.find('td[data-field="MATCH_MPN"] div').html(firstItem.MATCH_MPN === 'NA' ? '' : firstItem.MATCH_MPN);
                    }
                }

                select.empty().append(
                    '<option value="' + currentValue + '">' +
                    (currentValue || '请选择') +
                    '</option>'
                );

                data.forEach(function (item) {
                    if (item.MATCH_PARTNUMBER !== currentValue) {
                        // 创建更清晰的显示格式
                        var displayText = item.MATCH_PARTNUMBER || 'NA';
                        var detailInfo = [];

                        if (item.MATCH_CUST && item.MATCH_CUST !== 'NA') {
                            detailInfo.push('客户: ' + item.MATCH_CUST);
                        }
                        if (item.MATCH_MANUFACTURER && item.MATCH_MANUFACTURER !== 'NA') {
                            detailInfo.push('制造商: ' + item.MATCH_MANUFACTURER);
                        }
                        if (item.MATCH_MPN && item.MATCH_MPN !== 'NA') {
                            detailInfo.push('MPN: ' + item.MATCH_MPN);
                        }

                        if (detailInfo.length > 0) {
                            displayText += ' (' + detailInfo.join(', ') + ')';
                        }

                        select.append(
                            $('<option>', {
                                value: item.MATCH_PARTNUMBER,
                                text: displayText,
                                'data-cust': item.MATCH_CUST || '',
                                'data-manufacturer': item.MATCH_MANUFACTURER || '',
                                'data-mpn': item.MATCH_MPN || ''
                            })
                        );
                    }
                });

                // 根据选项数量决定是否显示下拉箭头和添加标识
                if (data.length > 1) {
                    wrapper.addClass('has-multiple');
                    // 添加多选项标识
                    if (!wrapper.find('.multiple-indicator').length) {
                        wrapper.append('<span class="multiple-indicator">多选项</span>');
                    }
                } else {
                    wrapper.removeClass('has-multiple');
                    // 添加单选项标识
                    if (!wrapper.find('.single-indicator').length) {
                        wrapper.append('<span class="single-indicator">单选项</span>');
                    }
                    // 移除多选项标识
                    wrapper.find('.multiple-indicator').remove();
                }

                form.render('select');
            });
        }

        form.on('select(matchSelect)', function (data) {
            var $select = $(data.elem);
            var rowId = $select.data('id');
            var $option = $select.find('option:selected');
            var $tr = $select.closest('tr');
            var index = $tr.data('index');

            // 更新其他三列的值
            var rowData = {
                MATCH_PARTNUMBER: data.value,
                MATCH_CUST: $option.data('cust'),
                MATCH_MANUFACTURER: $option.data('manufacturer'),
                MATCH_MPN: $option.data('mpn')
            };

            // 获取当前选中项的完整数据
            var selectedOptions = optionsData[rowId];

            // 重新构建select的选项
            $select.empty();

            // 添加一个默认选项，显示当前选中的MATCH_PARTNUMBER

            $select.append('<option value="' + data.value + '">' + data.value + '</option>');



            // 重新添加所有选项
            selectedOptions.forEach(function (item) {
                // 创建更清晰的显示格式
                var displayText = item.MATCH_PARTNUMBER || 'NA';
                var detailInfo = [];

                if (item.MATCH_CUST && item.MATCH_CUST !== 'NA') {
                    detailInfo.push('客户: ' + item.MATCH_CUST);
                }
                if (item.MATCH_MANUFACTURER && item.MATCH_MANUFACTURER !== 'NA') {
                    detailInfo.push('制造商: ' + item.MATCH_MANUFACTURER);
                }
                if (item.MATCH_MPN && item.MATCH_MPN !== 'NA') {
                    detailInfo.push('MPN: ' + item.MATCH_MPN);
                }

                if (detailInfo.length > 0) {
                    displayText += ' (' + detailInfo.join(', ') + ')';
                }

                // 如果不是当前选中项，则添加到下拉列表中
                if (item.MATCH_PARTNUMBER !== data.value) {
                    $select.append(
                        $('<option>', {
                            value: item.MATCH_PARTNUMBER,
                            text: displayText,
                            'data-cust': item.MATCH_CUST || '',
                            'data-manufacturer': item.MATCH_MANUFACTURER || '',
                            'data-mpn': item.MATCH_MPN || ''
                        })
                    );
                }
            });

            // 更新表格数据
            var tableData = table.cache['ComfirmTable'];
            Object.assign(tableData[index], rowData);

            // 更新其他列显示
            $tr.find('td[data-field="MATCH_CUST"] div').html(rowData.MATCH_CUST === 'NA' ? '' : rowData.MATCH_CUST);
            $tr.find('td[data-field="MATCH_MANUFACTURER"] div').html(rowData.MATCH_MANUFACTURER === 'NA' ? '' : rowData.MATCH_MANUFACTURER);
            $tr.find('td[data-field="MATCH_MPN"] div').html(rowData.MATCH_MPN === 'NA' ? '' : rowData.MATCH_MPN);

            // 重新渲染表单
            form.render('select');
        });


        // 刷新物料组数据
        $(document).on('click', '#RefreshGroups', function () {
            var $btn = $(this);
            $btn.prop('disabled', true).text('刷新中...');

            loadMaterialGroups().then(function(groups) {
                initializeQuickFillSelect();
                layer.msg('物料组数据已刷新', {icon: 1});
            }).catch(function(error) {
                layer.msg('刷新失败: ' + error, {icon: 2});
            }).finally(function() {
                $btn.prop('disabled', false).text('刷新物料组');
            });
        });

        // 修改一键填充物料组函数
        $(document).on('click', '#CopyGroup', function () {
            var selectedMaterialType = $('#MaterialType').val();
            if (!selectedMaterialType) {
                layer.msg('请先选择需要填充的物料组');
                return;
            }

            var checkStatus = table.checkStatus('ComfirmTable');
            var checkedData = checkStatus.data;

            if (checkedData.length === 0) {
                layer.msg('请至少选择一行数据');
                return;
            }

            checkedData.forEach(function (row) {
                var elemId = 'material-group-' + row.Id;
                var instance = materialGroupInstances[elemId];
                if (instance) {
                    // 查找对应的值
                    var targetValue = materialGroupsData.find(function(group) {
                        return group === selectedMaterialType;
                    });
                    if (targetValue) {
                        instance.setValue(targetValue);
                    }
                }
            });

            layer.msg('物料组填充完成，共填充 ' + checkedData.length + ' 行数据', {icon: 1});
        });

        // 验证物料组值是否有效
        function isValidMaterialGroup(value) {
            return materialGroupsData.includes(value);
        }





        // 修改 validateSelectedMaterialGroups 函数
        function validateSelectedMaterialGroups() {
            var checkStatus = table.checkStatus('ComfirmTable');
            var checkedData = checkStatus.data;

            if (checkedData.length === 0) {
                return {
                    valid: false,
                    message: '请至少选择一条数据'
                };
            }

            var invalidRows = [];
            checkedData.forEach(function (row) {
                var elemId = 'material-group-' + row.Id;
                var instance = materialGroupInstances[elemId];
                var value = '';

                if (instance) {
                    var result = instance.getValue();
                    value = result.value || '';
                }

                if (!value || !materialGroupsData.includes(value)) {
                    invalidRows.push(row.MATCH_PARTNUMBER || row.Id);
                }
            });

            return {
                valid: invalidRows.length === 0,
                message: invalidRows.length > 0 ?
                    `请正确填写选中行的物料组（料号: ${invalidRows.join(', ')}）` : ''
            };
        }



        // 下一步按钮点击事件 
        $('#nextbtn').on('click', function () {
            // 获取选中行数据
            var checkStatus = table.checkStatus('ComfirmTable');
            var selectedData = checkStatus.data;

            var validation = validateSelectedMaterialGroups();
            if (!validation.valid) {
                layer.msg(validation.message);
                return;
            }

            // 验证MATCH_PARTNUMBER
            var emptyMatchRows = selectedData.filter(row => !row.MATCH_PARTNUMBER);
            if (emptyMatchRows.length > 0) {
                layer.msg('请为选中的所有行选择匹配料号');
                return;
            }

            // 收集选中行的详细数据
            var updateData = selectedData.map(function (row) {
                var materialGroup = '';
                var elemId = 'material-group-' + row.Id;
                var instance = materialGroupInstances[elemId];

                if (instance) {
                    var result = instance.getValue();
                    materialGroup = result.value || '';
                }

                return {
                    Id: row.Id,
                    MATERIAL_GROUP: materialGroup,
                    MATCH_PARTNUMBER: $(`select[data-id="${row.Id}"]`).val(),
                    MATCH_CUST: row.MATCH_CUST || '',
                    MATCH_MANUFACTURER: row.MATCH_MANUFACTURER || '',
                    MATCH_MPN: row.MATCH_MPN || ''
                };
            });

            $.ajax({
                url: '../ashx/ConfirmControl.ashx?action=UpdateStatus&RFQNo=' + RFQNo,
                type: 'post',
                data: {
                    updateData: JSON.stringify({
                        updateData
                    })
                },
                success: function (data) {
                    console.log(data);
                    var objdata = eval("(" + data + ")");
                    if (objdata.result == "success") {
                        layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                            navigateInIframe("../Flow/SendQuote.aspx?RFQNo=" + RFQNo, "发送报价-" + RFQNo);
                        });
                    } else {
                        layer.alert(objdata.msg);
                    }
                },
                error: function (data) {
                    layer.alert("发生错误，请稍后重试");
                }
            });
        });

        // 窗口大小变化时调整表格高度
        $(window).on('resize', function() {
            if (table.cache['ComfirmTable']) {
                table.reload('ComfirmTable', {
                    height: Math.min(window.innerHeight - 250, 600)
                });
            }
        });

        // 初始化数据表格
        loadMaterialGroups().then(function() {
            initializeQuickFillSelect();
            getcolumndata();
        }).catch(function(error) {
            layer.msg('加载物料组数据失败，将使用默认数据', {icon: 2});
            // 使用默认数据
            materialGroupsData = ['主动型', '被动型', '其他'];
            initializeQuickFillSelect();
            getcolumndata();
        });
    });
</script>

