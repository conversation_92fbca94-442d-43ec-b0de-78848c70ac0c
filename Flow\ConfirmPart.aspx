<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ConfirmPart.aspx.cs" Inherits="WebApplication1.Flow.ConfirmPart" %>

<!DOCTYPE html>
<style>
    .table-container {
        width: 100%;
        overflow-x: auto;
        overflow-y: hidden; /* 修改为hidden */
    }
    

.layui-table-cell {
    overflow: visible !important;
    height: auto !important;
}

.layui-table {
    margin: 0 !important;
    width: 100% !important;
}

/* 确保表格容器正确展示 */
.layui-table-view {
    margin: 0 !important;
}

/* 调整表格盒子模型 */
.layui-table-box {
    position: relative;
    overflow: hidden !important;
    box-sizing: border-box;
}

/* 主体表格区域样式 */
.layui-table-main {
    min-height: 0 !important;
    overflow: auto !important;
}

/* 固定列样式调整 */
.layui-table-fixed {
    position: absolute;
    top: 0;
    z-index: 101;
    background-color: #fff;
    box-shadow: 1px 0 8px rgba(0,0,0,.08);
}

.layui-table-fixed-l {
    left: 0;
    box-shadow: none;
}

/* 表格单元格统一样式 */
.layui-table-cell {
    padding: 0 !important;
    height: 45px !important; /* 统一单元格高度 */
    line-height: 45px !important;
    position: relative;
    box-sizing: border-box;
    overflow: visible !important;
}

/* checkbox列的特殊处理 */
.layui-table-cell[lay-event="layTableAllChoose"],
.layui-table-cell[lay-event="layTableCheck"] {
    padding: 0 !important;
    height: 45px !important;
    line-height: 45px !important;
}

/* 确保checkbox垂直居中 */
.layui-form-checkbox[lay-skin="primary"] {
    margin-top: 0;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}

/* 表格行统一高度 */
.layui-table tr {
    height: 45px !important;
    line-height: 45px !important;
}

/* 下拉框容器样式调整 */
.match-select-wrapper,
.material-group-wrapper {
    height: 45px !important;
    line-height: 45px !important;
    position: relative;
    display: flex;
    align-items: center;
}

/* 确保下拉框选项正确显示 */
.layui-form-select dl {
    top: 45px !important;
}

/* 调整表头样式 */
.layui-table-header {
    overflow: hidden !important;
    position: relative;
    background-color: #f8f8f8;
}

/* 确保内容不被截断 */
.layui-table-cell .layui-form-select {
    height: 45px !important;
    line-height: 45px !important;
}

/* 设置表格边框 */
.layui-table-view {
    border: 1px solid #e6e6e6 !important;
}

/* 固定列同步滚动 */
.layui-table-fixed .layui-table-body {
    overflow: hidden !important;
    position: relative;
}

/* 确保所有行内容垂直居中 */
.layui-table-cell > * {
    vertical-align: middle !important;
}

/* 防止下拉框被截断 */
.layui-form-select .layui-input {
    height: 32px !important;
    line-height: 32px !important;
    margin-top: 6px;
}

/* 调整只针对下拉框的样式 */
.layui-form-select .layui-select {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999; /* 确保下拉框显示在其他内容上面 */
    width: auto; /* 确保下拉框宽度自动调整 */
    min-width: 100%; /* 确保下拉框至少和select输入框宽度相同 */
}

/* 可选：调整下拉框的最大高度，避免过长 */
.layui-form-select .layui-select dl {
    max-height: 200px;
    overflow-y: auto;
}
.bom-desc-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;  /* 添加手型光标提示可以查看更多 */
}
.match-select-wrapper, 
.material-group-wrapper {
    position: relative;
}
    
    /* 下拉框本身的样式 */
    .match-select-wrapper .layui-form-select {
        width: 100%;
    }
    
    /* 下拉框选项样式 */
    .match-select-wrapper .layui-form-select dl {
        max-width: none !important;  /* 移除最大宽度限制 */
        width: auto !important;      /* 自动宽度 */
        min-width: 100% !important;  /* 最小宽度与选择框相同 */
        white-space: nowrap;         /* 防止文字换行 */
    }
    
    /* 下拉选项样式 */
    .match-select-wrapper .layui-form-select dl dd {
        white-space: nowrap !important;  /* 防止文字换行 */
    }
    
    /* 自定义箭头样式 */
    .match-select-arrow {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #666;
        pointer-events: none;
        display: none;
        z-index: 1;
    }
    
    /* 隐藏 layui 默认箭头 */
    .match-select-wrapper .layui-form-select .layui-edge {
        display: none !important;
    }
    
    /* 当下拉框有多个选项时显示箭头 */
    .has-multiple .match-select-arrow {
        display: block;
    }
    
    /* 确保下拉面板显示在其他内容之上 */
    .layui-form-select dl {
        z-index: 999999 !important;
    }



/* 水平滚动条容器样式 */
.layui-table-main {
    overflow: auto !important; /* 改为auto */
}
/* 确保checkbox列固定在左侧 */
    
    /* 确保固定列不会产生滚动条 */
    .layui-table-fixed-l .layui-table-body,
    .layui-table-fixed-r .layui-table-body {
        overflow: hidden !important;
    }
  
</style>
<head runat="server">
<meta charset="utf-8">
    <title>共用料确认</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../lib/layui-v2.9.21/css/layui.css" media="all">
    <link rel="stylesheet" href="../css/public.css" media="all">
</head>
<body>
    <div class="layuimini-container">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
                <legend>快捷操作</legend>
                <div style="margin: 10px 10px 10px 10px">
                    <form class="layui-form layui-form-pane" id="rfqForm">

                        <div class="layui-form-item">
                            <label class="layui-form-label">物料组名</label>
                            <div class="layui-input-inline">
                                <select id="MaterialType">
                                    <option value="">请选择需要填充的物料组</option>
                                    <option value="主动型">主动型</option>
                                    <option value="被动型">被动型</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                            <button type="button" class="layui-btn layui-btn-normal" id="CopyGroup">一键填充物料</button>
                            
                        </div>
                    </form>
                </div>


            </fieldset>
        

        

        <table class="layui-hide" id="ComfirmTable" lay-filter="ComfirmTableFilter"></table>

        
        
    </div>
        <div class="container">
                <div class="layui-input-block">
                    
                    <button type="submit" id="nextbtn" class="layui-btn" lay-submit lay-filter="confirm-next-btn">
                        &emsp;下一步&emsp;
                                       <!-- 在表格之前添加测试用的select -->



                    </button>
                    
                </div>
            </div>
</div>
</body>
<script src="../lib/layui-v2.9.21/layui.js" charset="utf-8"></script>
<script src="../js/navigate.js" charset="utf-8"></script>
<script>
    layui.use(['form', 'table', 'upload', 'element'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table;

        function getQueryParam(name) {
            // 保留原有的参数获取逻辑
            var hash = window.location.hash;
            var hashParams = hash.substring(hash.indexOf('?') + 1);
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = hashParams.match(reg);

            if (r != null) {
                return decodeURIComponent(r[2]);
            }

            var queryParams = window.location.search.substr(1);
            r = queryParams.match(reg);
            if (r != null) {
                return decodeURIComponent(r[2]);
            }

            return null;
        }
        // 从URL获取RFQNo
        var RFQNo = getQueryParam('RFQNo');
        console.log(RFQNo);

        // 用一个对象来存储每个选项的完整信息
        var optionsData = {};
        // 定义允许的物料组值
        const ALLOWED_MATERIAL_GROUPS = ['主动型', '被动型', '其他'];
        // 在页面添加必要的样式
        // 修改物料组的样式
        $('head').append(`
        <style id="material-group-style">
            .material-group-wrapper {
                position: relative;
                width: 100%;
            }
            .material-group-input {
                width: calc(100% - 25px);
                height: 30px;
                line-height: 30px;
                border: 1px solid #e6e6e6;
                padding: 0 25px 0 5px;
                border-radius: 2px;
                background-color: #fff;
            }
            .material-group-input:focus {
                border-color: #009688;
            }
            .material-group-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    z-index: 999999 !important;
    width: 100% !important;
    background: #fff !important;
    border: 1px solid #e6e6e6 !important;
    max-height: 300px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.12) !important;
}
            .material-group-option {
                padding: 8px 10px;
                cursor: pointer;
                transition: background 0.2s;
            }
            .material-group-option:hover {
                background: #f2f2f2;
            }
            .material-group-toggle {
                position: absolute;
                right: 5px;
                top: 50%;
                transform: translateY(-50%);
                cursor: pointer;
                width: 20px;
                height: 20px;
                text-align: center;
                line-height: 20px;
                color: #666;
            }
            .invalid-input {
                border-color: #FF5722 !important;
            }
        </style>
    `);

        function getcolumndata() {  //type=1 加载数据和列 type=0 仅加载列
            var index = layer.load(1, { shade: [0.5, '#fff'] });  // 显示加载动画
            console.log('添加前');
            columns = [];
            $.ajax({
                url: '../ashx/ConfirmControl.ashx?action=QueryColumn&RFQNo=' + RFQNo,
                type: 'POST',
                success: function (response) {

                    console.log(response);
                    if (JSON.parse(response).result == 'success') {
                        var fielddata = JSON.parse(response).col; // 假设返回的是JSON格式的数据
                        var modeldata = JSON.parse(response).models;
                        console.log(modeldata);
                        columns.push({ type: 'checkbox', fixed: 'left'});
                        columns.push({ field: 'Id', minWidth: 50, title: 'Id', hide: true });
                        // 添加MATCH相关列
                        columns.push(
                            {
                                field: 'MATERIAL_GROUP',
                                title: '物料组',
                                width: 150,
                                templet: function (d) {
                                    var html = '<div class="material-group-wrapper">';
                                    html += '<input type="text" class="material-group-input" ';
                                    html += 'value="' + (d.MATERIAL_GROUP || '') + '" ';
                                    html += 'data-id="' + d.Id + '" ';
                                    html += 'autocomplete="off">';
                                    html += '<span class="material-group-toggle">▼</span>';
                                    html += '<div class="material-group-dropdown">';
                                    ALLOWED_MATERIAL_GROUPS.forEach(function (value) {
                                        html += '<div class="material-group-option">' + value + '</div>';
                                    });
                                    html += '</div>';
                                    html += '</div>';
                                    return html;
                                }
                            },
                            {
                                field: 'MATCH_PARTNUMBER',
                                title: '匹配料号',
                                width: 220,
                                templet: function (d) {
                                    var currentValue = d.MATCH_PARTNUMBER || '';
                                    var selectHtml = '<div class="match-select-wrapper" data-id="' + d.Id + '">';
                                    selectHtml += '<select lay-filter="matchSelect" class="match-select layui-select" lay-search data-id="' + d.Id + '">';
                                    selectHtml += '<option value="' + currentValue + '">' + (currentValue || '请选择') + '</option>';
                                    selectHtml += '</select>';
                                    selectHtml += '<span class="match-select-arrow"></span>';
                                    selectHtml += '</div>';
                                    return selectHtml;
                                }
                            },
                            { field: 'MATCH_CUST', title: '客户料号', width: 150 },
                            { field: 'MATCH_MANUFACTURER', title: '制造商', width: 150 },
                            { field: 'MATCH_MPN', title: 'MPN', width: 150 }

                        );
                        if (fielddata.length > 0) {

                            fielddata.forEach(function (fd) {
                                if (fd.DatabaseField == 'BOM_DESC') {
                                    columns.push({
                                        field: fd.DatabaseField, align: 'center', minWidth: 170, title: fd.DatabaseField, templet: function (d) {
                                            // 使用data属性存储完整文本
                                            return '<div class="bom-desc-cell" data-content="' +
                                                (d.BOM_DESC || '').replace(/"/g, '&quot;') +
                                                '" lay-tips="' + (d.BOM_DESC || '').replace(/"/g, '&quot;') +
                                                '">' + d.BOM_DESC + '</div>';
                                        }
                                    });
                                }
                                else {
                                    columns.push({ field: fd.DatabaseField, align: 'center', minWidth: 200, title: fd.DatabaseField });
                                }
                            });
                        }
                        if (modeldata.length > 0) {
                            for (var key in modeldata) {

                                columns.push({ field: modeldata[key], align: 'center', minWidth: 150, title: modeldata[key] });
                            }

                        }


                        $.ajax({
                            url: '../ashx/ConfirmControl.ashx?action=QueryBom&RFQNo=' + RFQNo,
                            type: 'POST',
                            success: function (bomdata) {
                                console.log(columns);
                                rendertable(JSON.parse(bomdata), columns);
                                layer.close(index);  // 关闭加载动画
                            },
                            error: function (data) {
                                layer.alert(data.msg);
                                layer.close(index);  // 关闭加载动画
                            }
                        });



                    }
                    else {
                        layer.alert("RFQNo为空,请先导入项目信息");
                        layer.close(index);  // 关闭加载动画
                    }

                },
                error: function (data) {
                    layer.alert(data.msg);
                    layer.close(index);  // 关闭加载动画
                    $('button').prop('disabled', false);  // 恢复所有按钮
                }
            });


        }
        // 重写物料组输入框初始化函数
        function initializeMaterialGroupInputs() {
            // 移除所有已有的事件绑定
            $(document).off('.materialGroup');

            // 点击下拉箭头显示/隐藏选项
            $(document).on('click.materialGroup', '.material-group-toggle', function (e) {
                e.stopPropagation();
                const $wrapper = $(this).closest('.material-group-wrapper');
                const $dropdown = $wrapper.find('.material-group-dropdown');
                const $input = $wrapper.find('.material-group-input');

                // 切换下拉框显示状态
                $('.material-group-dropdown').not($dropdown).hide();
                $dropdown.toggle();

                // 更新箭头方向
                $(this).html($dropdown.is(':visible') ? '▲' : '▼');
            });

            // 点击选项时更新输入框的值
            $(document).on('click.materialGroup', '.material-group-option', function (e) {
                e.stopPropagation();
                const value = $(this).text();
                const $wrapper = $(this).closest('.material-group-wrapper');
                const $input = $wrapper.find('.material-group-input');
                const $toggle = $wrapper.find('.material-group-toggle');

                // 设置输入框的值
                $input.val(value);

                // 隐藏下拉框并更新箭头
                $wrapper.find('.material-group-dropdown').hide();
                $toggle.html('▼');

                // 触发change事件
                $input.trigger('change');
            });

            // 点击输入框显示选项
            $(document).on('click.materialGroup', '.material-group-input', function (e) {
                e.stopPropagation();
                const $wrapper = $(this).closest('.material-group-wrapper');
                const $dropdown = $wrapper.find('.material-group-dropdown');
                const $toggle = $wrapper.find('.material-group-toggle');

                // 显示下拉框并更新箭头
                $('.material-group-dropdown').not($dropdown).hide();
                $('.material-group-toggle').html('▼');
                $dropdown.show();
                $toggle.html('▲');
            });

            // 点击页面其他地方时隐藏所有下拉框
            $(document).on('click.materialGroup', function (e) {
                if (!$(e.target).closest('.material-group-wrapper').length) {
                    $('.material-group-dropdown').hide();
                    $('.material-group-toggle').html('▼');
                }
            });

            // 输入验证
            $(document).on('input.materialGroup', '.material-group-input', function () {
                var value = $(this).val().trim();
                if (value && !ALLOWED_MATERIAL_GROUPS.includes(value)) {
                    $(this).addClass('invalid-input');
                } else {
                    $(this).removeClass('invalid-input');
                }
            });

            // 失去焦点时验证
            $(document).on('blur.materialGroup', '.material-group-input', function () {
                var value = $(this).val().trim();
                if (value && !ALLOWED_MATERIAL_GROUPS.includes(value)) {
                    $(this).val('');
                    layer.msg('请输入有效的物料组值：主动型、被动型、其他');
                }
            });
        }


        function rendertable(data, columns) {
            // 确保清除可能存在的旧实例
            if (table.cache['ComfirmTable']) {
                delete table.cache['ComfirmTable'];
            }
            table.render({
                elem: '#ComfirmTable',
                cols: [columns],
                data: data,
                page: false,
                height: 500,
                limit: 10000, // 设置一个很大的数值，显示所有数据
                done: function (res) {
                    

                    // 初始化其他功能
                    $('.material-group-dropdown').hide();

                    // 处理提示框
                    $('.bom-desc-cell').each(function () {
                        var content = $(this).data('content');
                        if (content) {
                            layui.use('layer', function () {
                                var layer = layui.layer;
                                $(this).on('mouseenter', function () {
                                    layer.tips(content, this, {
                                        tips: [1, '#3595CC'],
                                        time: 0,
                                        maxWidth: 500
                                    });
                                }).on('mouseleave', function () {
                                    layer.closeAll('tips');
                                });
                            });
                        }
                    });
                    setTimeout(function () {
                        loadMatchPartnumberOptions();
                        initializeMaterialGroupInputs();
                        // 默认全选
                        $('.layui-table-header [lay-filter="layTableAllChoose"]').click();
                    }, 50);
                }
                   
                
            });
        }

        // 在表格初始化完成后添加以下代码
        table.on('done', function (res, curr, count) {
            // 同步固定列和主体的高度
            var fixedNodes = document.querySelectorAll('.layui-table-fixed .layui-table-body tr');
            var mainNodes = document.querySelectorAll('.layui-table-main tr');

            for (var i = 0; i < fixedNodes.length; i++) {
                if (mainNodes[i]) {
                    var mainHeight = mainNodes[i].offsetHeight;
                    fixedNodes[i].style.height = mainHeight + 'px';
                }
            }

            // 监听表格滚动
            $('.layui-table-main').on('scroll', function () {
                var top = $(this).scrollTop();
                $('.layui-table-fixed .layui-table-body').scrollTop(top);
            });
        });

        // 在选择框变化时重新同步高度
        form.on('select(matchSelect)', function (data) {
            setTimeout(function () {
                table.resize('ComfirmTable');
            }, 10);
        });

        function loadMatchPartnumberOptions() {
            $('.match-select').each(function () {
                var select = $(this);
                var rowId = select.data('id');
                var currentValue = select.find('option:first').text();
                var wrapper = select.closest('.match-select-wrapper');

                select.empty().append('<option value="">加载中...</option>');

                $.ajax({
                    url: '../ashx/ConfirmControl.ashx?action=GetMatchOptions',
                    type: 'POST',
                    data: { id: rowId },
                    success: function (response) {
                        var data = JSON.parse(response);
                        optionsData[rowId] = data;

                        if (data.length > 0 && (!currentValue || currentValue === '请选择' || currentValue === '加载中...')) {
                            var firstItem = data[0];
                            currentValue = firstItem.MATCH_PARTNUMBER;

                            var $tr = select.closest('tr');
                            var index = $tr.data('index');
                            var tableData = table.cache['ComfirmTable'];

                            Object.assign(tableData[index], {
                                MATCH_PARTNUMBER: firstItem.MATCH_PARTNUMBER,
                                MATCH_CUST: firstItem.MATCH_CUST,
                                MATCH_MANUFACTURER: firstItem.MATCH_MANUFACTURER,
                                MATCH_MPN: firstItem.MATCH_MPN
                            });

                            $tr.find('td[data-field="MATCH_CUST"] div').html(firstItem.MATCH_CUST === 'NA' ? '' : firstItem.MATCH_CUST);
                            $tr.find('td[data-field="MATCH_MANUFACTURER"] div').html(firstItem.MATCH_MANUFACTURER === 'NA' ? '' : firstItem.MATCH_MANUFACTURER);
                            $tr.find('td[data-field="MATCH_MPN"] div').html(firstItem.MATCH_MPN === 'NA' ? '' : firstItem.MATCH_MPN);
                        }

                        select.empty().append(
                            '<option value="' + currentValue + '">' +
                            (currentValue || '请选择') +
                            '</option>'
                        );

                        data.forEach(function (item) {
                            if (item.MATCH_PARTNUMBER !== currentValue) {
                                var displayText = [
                                    item.MATCH_PARTNUMBER || 'NA',
                                    item.MATCH_CUST || 'NA',
                                    item.MATCH_MANUFACTURER || 'NA',
                                    item.MATCH_MPN || 'NA'
                                ].join(' | ');

                                select.append(
                                    $('<option>', {
                                        value: item.MATCH_PARTNUMBER,
                                        text: displayText,
                                        'data-cust': item.MATCH_CUST || '',
                                        'data-manufacturer': item.MATCH_MANUFACTURER || '',
                                        'data-mpn': item.MATCH_MPN || ''
                                    })
                                );
                            }
                        });

                        // 根据选项数量决定是否显示下拉箭头
                        if (data.length > 1) {
                            wrapper.addClass('has-multiple');
                        } else {
                            wrapper.removeClass('has-multiple');
                        }

                        form.render('select');
                    },
                    error: function () {
                        select.empty().append('<option value="">加载失败</option>');
                        wrapper.removeClass('has-multiple');
                    }
                });
            });
        }

        form.on('select(matchSelect)', function (data) {
            var $select = $(data.elem);
            var rowId = $select.data('id');
            var $option = $select.find('option:selected');
            var $tr = $select.closest('tr');
            var index = $tr.data('index');

            // 更新其他三列的值
            var rowData = {
                MATCH_PARTNUMBER: data.value,
                MATCH_CUST: $option.data('cust'),
                MATCH_MANUFACTURER: $option.data('manufacturer'),
                MATCH_MPN: $option.data('mpn')
            };

            // 获取当前选中项的完整数据
            var selectedOptions = optionsData[rowId];

            // 重新构建select的选项
            $select.empty();

            // 添加一个默认选项，显示当前选中的MATCH_PARTNUMBER

            $select.append('<option value="' + data.value + '">' + data.value + '</option>');



            // 重新添加所有选项
            selectedOptions.forEach(function (item) {
                var displayText = [
                    item.MATCH_PARTNUMBER || 'NA',
                    item.MATCH_CUST || 'NA',
                    item.MATCH_MANUFACTURER || 'NA',
                    item.MATCH_MPN || 'NA'
                ].join(' | ');

                // 如果不是当前选中项，则添加到下拉列表中
                if (item.MATCH_PARTNUMBER !== data.value) {
                    $select.append(
                        $('<option>', {
                            value: item.MATCH_PARTNUMBER,
                            text: displayText,
                            'data-cust': item.MATCH_CUST || '',
                            'data-manufacturer': item.MATCH_MANUFACTURER || '',
                            'data-mpn': item.MATCH_MPN || ''
                        })
                    );
                }
            });

            // 更新表格数据
            var tableData = table.cache['ComfirmTable'];
            Object.assign(tableData[index], rowData);

            // 更新其他列显示
            $tr.find('td[data-field="MATCH_CUST"] div').html(rowData.MATCH_CUST === 'NA' ? '' : rowData.MATCH_CUST);
            $tr.find('td[data-field="MATCH_MANUFACTURER"] div').html(rowData.MATCH_MANUFACTURER === 'NA' ? '' : rowData.MATCH_MANUFACTURER);
            $tr.find('td[data-field="MATCH_MPN"] div').html(rowData.MATCH_MPN === 'NA' ? '' : rowData.MATCH_MPN);

            // 重新渲染表单
            form.render('select');
        });


        // 修改一键填充物料组函数
        $(document).on('click', '#CopyGroup', function () {
            var selectedMaterialType = $('#MaterialType').val();
            if (!selectedMaterialType) {
                layer.msg('请先选择需要填充的物料组');
                return;
            }

            var checkStatus = table.checkStatus('ComfirmTable');
            var checkedData = checkStatus.data;

            if (checkedData.length === 0) {
                layer.msg('请至少选择一行数据');
                return;
            }

            checkedData.forEach(function (row) {
                $('.material-group-input[data-id="' + row.Id + '"]').val(selectedMaterialType);
            });

            layer.msg('物料组填充完成');
        });

        // 验证物料组值是否有效
        function isValidMaterialGroup(value) {
            return ALLOWED_MATERIAL_GROUPS.includes(value);
        }

        // 监听物料组选择变化
        form.on('select(materialGroup)', function (data) {
            var value = data.value;
            if (value && !isValidMaterialGroup(value)) {
                $(data.elem).val('');
                form.render('select');
                layer.msg('无效的物料组值');
            }
        });





        // 修改 validateSelectedMaterialGroups 函数
        function validateSelectedMaterialGroups() {
            var checkStatus = table.checkStatus('ComfirmTable');
            var checkedData = checkStatus.data;

            if (checkedData.length === 0) {
                return {
                    valid: false,
                    message: '请至少选择一条数据'
                };
            }

            var invalidRows = [];
            checkedData.forEach(function (row) {
                var input = $('.material-group-input[data-id="' + row.Id + '"]');
                var value = input.val().trim();
                if (!value || !ALLOWED_MATERIAL_GROUPS.includes(value)) {
                    invalidRows.push(row.MATCH_PARTNUMBER);
                }
            });

            return {
                valid: invalidRows.length === 0,
                message: invalidRows.length > 0 ?
                    `请正确填写选中行的物料组（行ID: ${invalidRows.join(', ')}）` : ''
            };
        }



        // 下一步按钮点击事件 
        $('#nextbtn').on('click', function () {
            // 获取选中行数据
            var checkStatus = table.checkStatus('ComfirmTable');
            var selectedData = checkStatus.data;

            var validation = validateSelectedMaterialGroups();
            if (!validation.valid) {
                layer.msg(validation.message);
                return;
            }

            // 验证MATCH_PARTNUMBER
            var emptyMatchRows = selectedData.filter(row => !row.MATCH_PARTNUMBER);
            if (emptyMatchRows.length > 0) {
                layer.msg('请为选中的所有行选择匹配料号');
                return;
            }

            // 收集选中行的详细数据
            var updateData = selectedData.map(function (row) {
                return {
                    Id: row.Id,
                    MATERIAL_GROUP: $(`.material-group-input[data-id="${row.Id}"]`).val(),
                    MATCH_PARTNUMBER: $(`select[data-id="${row.Id}"]`).val(),
                    MATCH_CUST: row.MATCH_CUST || '',
                    MATCH_MANUFACTURER: row.MATCH_MANUFACTURER || '',
                    MATCH_MPN: row.MATCH_MPN || ''
                };
            });

            $.ajax({
                url: '../ashx/ConfirmControl.ashx?action=UpdateStatus&RFQNo=' + RFQNo,
                type: 'post',
                data: {
                    updateData: JSON.stringify({
                        updateData
                    })
                },
                success: function (data) {
                    console.log(data);
                    var objdata = eval("(" + data + ")");
                    if (objdata.result == "success") {
                        layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                            navigateInIframe("../Flow/SendQuote.aspx?RFQNo=" + RFQNo, "发送报价-" + RFQNo);
                        });
                    } else {
                        layer.alert(objdata.msg);
                    }
                },
                error: function (data) {
                    layer.alert("发生错误，请稍后重试");
                }
            });
        });

        // 初始化数据表格
        getcolumndata();
    });
</script>

